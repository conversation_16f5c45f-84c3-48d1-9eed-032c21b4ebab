import 'dart:io';

import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/page/book_detail.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/delete_confirm.dart';
import 'package:dasso_reader/widgets/icon_and_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:icons_plus/icons_plus.dart';

class BookBottomSheet extends ConsumerWidget {
  const BookBottomSheet({
    super.key,
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<void> handleDelete(BuildContext context) async {
      Navigator.pop(context);
      await updateBook(
        Book(
          id: book.id,
          title: book.title,
          coverPath: book.coverPath,
          filePath: book.filePath,
          lastReadPosition: book.lastReadPosition,
          readingPercentage: book.readingPercentage,
          author: book.author,
          isDeleted: true,
          description: book.description,
          rating: book.rating,
          createTime: book.createTime,
          updateTime: DateTime.now(),
        ),
      );
      ref.read(bookListProvider.notifier).refresh();
      File(book.fileFullPath).delete();
      File(book.coverFullPath).delete();
    }

    void handleDetail(BuildContext context) {
      Navigator.pop(context);
      Navigator.push(
        context,
        MaterialPageRoute<void>(
          builder: (context) => BookDetail(book: book),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(DesignSystem.spaceL),
      height: 100,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          bookCover(
            context,
            book,
            width: DesignSystem.widgetIconSizeLarge + DesignSystem.spaceS,
          ),
          const SizedBox(width: DesignSystem.spaceS),
          Expanded(
            child: Text(
              book.title,
              style: Theme.of(context).textTheme.titleMedium,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SemanticHelpers.button(
            context: context,
            label: 'Book details',
            hint: 'View detailed information about ${book.title}',
            onTap: () {
              handleDetail(context);
            },
            child: IconAndText(
              icon: const Icon(EvaIcons.more_vertical),
              text: L10n.of(context).notes_page_detail,
              onTap: () {
                handleDetail(context);
              },
            ),
          ),
          DeleteConfirm(
            delete: () {
              handleDelete(context);
            },
            deleteIcon: IconAndText(
              icon: const Icon(EvaIcons.trash),
              text: L10n.of(context).common_delete,
            ),
            confirmIcon: IconAndText(
              icon: const Icon(
                EvaIcons.checkmark_circle_2,
                color: Colors.red,
              ),
              text: L10n.of(context).common_confirm,
            ),
          ),
        ],
      ),
    );
  }
}

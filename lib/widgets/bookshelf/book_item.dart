import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/app_typography.dart';
import 'package:dasso_reader/widgets/bookshelf/book_bottom_sheet.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookItem extends ConsumerWidget {
  const BookItem({
    super.key,
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<void> handleLongPress(BuildContext context) async {
      showModalBottomSheet<void>(
        context: context,
        builder: (BuildContext context) {
          return BookBottomSheet(book: book);
        },
      );
    }

    // Extract colors from theme for Material 3 compliance
    final colorScheme = Theme.of(context).colorScheme;

    return SemanticHelpers.book(
      title: book.title,
      author: book.author,
      progress: book.readingPercentage,
      onTap: () {
        openBook(context, book, ref);
        // Provide haptic feedback for better accessibility
        HapticFeedback.mediumImpact();
      },
      onLongPress: () {
        handleLongPress(context);
        // Provide haptic feedback for better accessibility
        HapticFeedback.heavyImpact();
      },
      child: GestureDetector(
        onTap: () {
          openBook(context, book, ref);
          // Provide haptic feedback for better accessibility
          HapticFeedback.mediumImpact();
        },
        onLongPress: () {
          handleLongPress(context);
          // Provide haptic feedback for better accessibility
          HapticFeedback.heavyImpact();
        },
        onSecondaryTap: () {
          handleLongPress(context);
        },
        // Make minimum touch area at least 48x48 for accessibility
        behavior: HitTestBehavior.opaque,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Hero(
                tag: book.coverFullPath,
                child: Container(
                  decoration: BoxDecoration(
                    // Use Material 3 elevation and shadow with DesignSystem values
                    boxShadow: [
                      BoxShadow(
                        color:
                            colorScheme.shadow.withAlpha(51), // 0.2 * 255 = 51
                        spreadRadius: 1,
                        blurRadius:
                            DesignSystem.spaceS, // 8.0 (preserves exact blur)
                        offset: const Offset(
                          0,
                          DesignSystem.elevationXS + 1,
                        ), // (0, 2) (preserves exact offset)
                      ),
                    ],
                  ),
                  child: Material(
                    // Add material for proper states and feedback
                    color: Colors.transparent,
                    child: Row(
                      children: [
                        Expanded(
                          child: SemanticHelpers.image(
                            label: 'Book cover for ${book.title}',
                            child: bookCover(context, book),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: DesignSystem.spaceS,
            ), // 8.0 (preserves exact spacing)
            ConstrainedBox(
              constraints: const BoxConstraints(
                minHeight: DesignSystem.widgetIconSizeMedium - 4,
              ), // 20.0 (preserves exact height)
              child: Text(
                book.title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  // Use the color scheme for text
                  color: colorScheme.onSurface,
                ),
              ),
            ),
            ConstrainedBox(
              constraints: const BoxConstraints(
                minHeight: DesignSystem.spaceM,
              ), // 16.0 (preserves exact height)
              child: _BookMetadata(
                author: book.author,
                readingPercentage: book.readingPercentage,
                colorScheme: colorScheme,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Optimized metadata widget that only rebuilds when book metadata changes
///
/// This widget is extracted to minimize rebuilds and can use const constructor
/// when the data is static.
class _BookMetadata extends StatelessWidget {
  const _BookMetadata({
    required this.author,
    required this.readingPercentage,
    required this.colorScheme,
  });

  final String author;
  final double readingPercentage;
  final ColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            author,
            style: AppTypography.labelSmall.copyWith(
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w300),
              color: colorScheme.onSurfaceVariant,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        Text(
          '${(readingPercentage * 100).toStringAsFixed(0)}%',
          style: AppTypography.labelSmall.copyWith(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w300),
            color: colorScheme.primary, // Use primary color for emphasis
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

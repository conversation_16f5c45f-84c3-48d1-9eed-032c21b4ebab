import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dasso_reader/config/design_system.dart';

Widget linkIcon({
  required Widget icon,
  required String url,
  required LaunchMode mode,
  double? size, // Made nullable to use DesignSystem default
}) {
  // Use DesignSystem constant with manufacturer adjustment
  final adjustedSize = size ?? DesignSystem.getAdjustedIconSize(30.0);
  return IconButton(
    onPressed: () => launchUrl(
      Uri.parse(url),
      mode: mode,
    ),
    icon: SizedBox(
      width: adjustedSize,
      height: adjustedSize,
      child: icon,
    ),
  );
}

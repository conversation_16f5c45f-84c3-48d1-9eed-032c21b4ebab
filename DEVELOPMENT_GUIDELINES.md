# 🚀 DassoShu Reader - Flutter Development Guidelines (Enhanced)

## 📋 Project Overview
**DassoShu Reader**: Professional Chinese language learning e-book reader
- **Flutter SDK**: `>=3.5.2 <4.0.0`
- **Material Design**: Material 3 with `useMaterial3: true`
- **State Management**: Flutter Riverpod 2.5.1+ with code generation
- **Chinese Support**: `chinese_font_library` for proper font rendering

## 🎯 **CORE PRINCIPLES** (ALWAYS FOLLOW)

### **1. 🚫 NEVER BREAK EXISTING FUNCTIONALITY**
- **Preserve everything** - maintain all existing features and behaviors
- **Respect existing architecture** - work within established patterns
- **Maintain backward compatibility** - ensure smooth upgrades
- **Test thoroughly** before implementing changes

### **2. 🎨 MATERIAL DESIGN 3 & FLUTTER BEST PRACTICES**
- **Follow Material Design 3** principles and guidelines
- **Use Flutter best practices** for widget composition and state management
- **Implement proper accessibility** with semantic labels and tooltips
- **Optimize performance** with const constructors and efficient rebuilds

### **3. 📱 SPACE EFFICIENCY & CLEAN UX**
- **Prioritize vertical space** optimization in all UI components
- **Choose icons with tooltips** over text labels for modern, clean look
- **Use lowercase naming** conventions (e.g., 'Char' not 'CHAR')

### **4. 🔧 PROFESSIONAL DEVELOPMENT APPROACH**
- **Ask before major changes** - present options with professional analysis
- **Provide detailed explanations** and implementation summaries
- **Present multiple solutions** with UI/UX recommendations
- **Implement exactly what user chooses** after presenting options

---

## 🏗️ **ARCHITECTURE & CODE QUALITY**

### **Code Standards**
```dart
// ✅ GOOD - Use DesignSystem constants
EdgeInsets.all(DesignSystem.spaceM)
// ❌ BAD - Hardcoded values
EdgeInsets.all(16.0)
```

- Follow lint rules in `analysis_options.yaml`
- Use `dart format` for consistent formatting
- Document public APIs with Dartdoc comments (`///`)
- Keep functions short and focused on single responsibility
- Minimize code duplication using functions, classes, mixins

### **Enhanced Lint Rules (2025 Update)**
Our codebase now includes **comprehensive lint enforcement**:
- **1,865 const constructors** implemented for optimal performance
- **959 DesignSystem references** ensuring consistency
- **Zero hardcoded values** - all replaced with DesignSystem constants
- **Automated quality checks** catch violations during development

### **Widget Best Practices**
- **Use `const` constructors** when possible to prevent unnecessary rebuilds
- **Break down large widgets** into smaller, manageable sub-widgets
- **Call `setState()`** only when necessary and as locally as possible
- **Use proper keys** for widget lists and state preservation
- **Prefer StatelessWidget** when no internal state management needed

### **State Management (Riverpod)**
- **Use `Provider`** for simple dependencies
- **Use `StateProvider`** for simple state
- **Use `StateNotifierProvider`** for complex state
- **Use `FutureProvider`** for async data
- **Implement proper disposal** to prevent memory leaks

---

## 🎨 **DESIGN SYSTEM COMPLIANCE**

### **Mandatory DesignSystem Usage**
```dart
// ✅ ALWAYS use DesignSystem constants
padding: EdgeInsets.all(DesignSystem.spaceM)
borderRadius: BorderRadius.circular(DesignSystem.radiusM)
fontSize: DesignSystem.fontSizeM

// ✅ Use adaptive methods for responsive design
padding: DesignSystem.getAdaptivePadding(context)
fontSize: DesignSystem.getAdjustedFontSize(context, DesignSystem.fontSizeM)
```

### **Specialized Design Extensions**
- **SettingsDesign** - for settings pages
- **ReadingDesign** - for reading interface
- **WidgetDesign** - for reusable components

### **Pixel-Perfect Manufacturer Adjustments**
```dart
// ✅ Use manufacturer-specific adjustments
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)
iconSize: DesignSystem.getAdjustedIconSize(24.0)
elevation: DesignSystem.getAdjustedElevation(4.0)
```

### **Accessibility Requirements**
- **44dp minimum touch targets** for all interactive elements
- **Proper semantic labels** for screen readers
- **Tooltips for icons** to provide context
- **WCAG AAA compliance** for color contrast

---

## 📱 **PLATFORM ADAPTATIONS**

### **Use Adaptive Components**
```dart
// ✅ Platform-appropriate navigation
AdaptiveNavigation.push(context, MyPage());

// ✅ Platform-specific icons
Icon(AdaptiveIcons.back)
Icon(AdaptiveIcons.settings)

// ✅ Platform-appropriate dialogs
AdaptiveDialogs.showAlert(context: context, title: '...', content: '...');
```

### **Platform-Specific Behaviors**
- **iOS**: CupertinoPageRoute, flat design, BouncingScrollPhysics
- **Android**: MaterialPageRoute, elevation, ClampingScrollPhysics
- **Cross-platform consistency** while respecting platform conventions

---

## ⚡ **PERFORMANCE & OPTIMIZATION**

### **Performance Monitoring**
```dart
// ✅ Use appropriate logging modes
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet); // Development
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose); // Debugging
```

### **Error Handling**
```dart
// ✅ Proper error handling
try {
  await riskyOperation();
} catch (e) {
  AnxLog.severe('Operation failed: $e');
  // Provide user-friendly feedback
  AnxToast.show(L10n.of(context).error_message);
}
```

### **Memory Management**
- **Dispose controllers** and listeners properly
- **Use efficient scroll physics** and proper widget lifecycle
- **Monitor performance** using the performance dashboard
- **Optimize images** and assets for different screen densities

---

## 🔒 **SECURITY & DATA HANDLING**

### **Secure Storage**
```dart
// ✅ Use secure storage for sensitive data
await FlutterSecureStorage().write(key: 'api_key', value: apiKey);
```

### **Network Security**
- **Use HTTPS** for all network communication
- **Validate all user input** on both client and server side
- **Handle API keys securely** without hardcoding in client-side code
- **Implement proper authentication** and authorization

---

## 📝 **DOCUMENTATION & TESTING**

### **Documentation Requirements**
```dart
/// Builds a compact tab bar for the combined row layout.
/// 
/// This widget creates a space-efficient tab bar that combines
/// action buttons and tabs in a single row to save vertical space.
/// 
/// Example:
/// ```dart
/// _buildCompactTabBar(context, selectedText, bookId, readingTextColor)
/// ```
Widget _buildCompactTabBar(BuildContext context, String selectedText) {
  // Implementation
}
```

### **Testing Strategy**
- **Unit tests** for critical business logic
- **Widget tests** for important UI components
- **Integration tests** for complete user flows
- **Cross-device testing** on multiple manufacturers
- **Accessibility testing** with screen readers

---

## 🚀 **BUILD & DEPLOYMENT**

### **Version Control**
```bash
# ✅ Proper commit message format
git commit -m "feat(context-menu): add icons with tooltips for compact layout

- Replace text labels with Material Design icons
- Add tooltips for accessibility
- Implement space-efficient tab bar design
- Maintain all existing functionality"
```

### **Dependency Management**
```yaml
# ✅ Use package managers, never edit manually
dependencies:
  flutter:
    sdk: flutter
  riverpod: ^2.4.0  # Use caret for stable versions
```

### **Build Process**
- **Use Flutter flavors** for different environments
- **Implement proper signing** configuration
- **Automate versioning** and build process
- **Generate build reports** for size and performance metrics

---

## 🎯 **SPECIFIC PROJECT PREFERENCES**

### **Context Menu Guidelines**
- **Unified context menu** is a critical component - handle with extreme care
- **Save vertical space** by combining elements when possible
- **Use icons with tooltips** instead of text labels
- **Maintain all existing functionality** while improving UX

### **Chinese Learning App Specifics**
- **Support both simplified and traditional** Chinese text
- **Implement proper Chinese font rendering** with chinese_font_library
- **Handle polyphonic characters** correctly
- **Provide proper pinyin display** and pronunciation support

### **AI Services Type Safety (Critical)**
Following comprehensive type safety improvements (Sessions 9-13):

#### **Stream Processing Safety**
```dart
// ✅ MANDATORY: Type-safe AI stream processing
Stream<String> aiServiceStream() async* {
  final typedStream = stream ?? const Stream<Uint8List>.empty();
  final stringStream = typedStream.transform(
    StreamTransformer<Uint8List, String>.fromHandlers(
      handleData: (Uint8List data, EventSink<String> sink) {
        sink.add(utf8.decode(data));
      },
    ),
  );

  await for (final chunk in stringStream) {
    final chunkString = chunk as String? ?? '';
    yield processedContent; // Properly typed String
  }
}
```

#### **Dynamic Collection Safety**
```dart
// ✅ MANDATORY: Safe iteration over dynamic collections
final configMap = data['config'] as Map<String, dynamic>?;
final configKeys = configMap?.keys.toList() ?? <String>[];
for (final key in configKeys) { /* Safe iteration */ }
```

#### **Closure Return Type Safety**
```dart
// ✅ MANDATORY: Explicit return type casting in closures
keys.sort((a, b) {
  final valueA = (data[a]['timestamp'] as num?)?.toInt() ?? 0;
  final valueB = (data[b]['timestamp'] as num?)?.toInt() ?? 0;
  return valueA - valueB; // Returns int, not dynamic
});
```

**📚 Complete Guide**: See `docs/AI_SERVICES_TYPE_SAFETY_GUIDE.md`

### **UI/UX Priorities**
1. **Functionality preservation** (highest priority)
2. **Space efficiency** and clean design
3. **Accessibility** and usability
4. **Performance** and responsiveness
5. **Cross-platform consistency**

---

## 📋 **QUALITY CHECKLIST**

Before submitting any code, ensure:

- [ ] **No breaking changes** - all existing functionality works
- [ ] **DesignSystem compliance** - no hardcoded values
- [ ] **Accessibility** - proper semantic labels and tooltips
- [ ] **Performance** - efficient rebuilds and memory usage
- [ ] **Platform adaptation** - works correctly on iOS and Android
- [ ] **Documentation** - clear comments and explanations
- [ ] **Testing** - appropriate test coverage
- [ ] **Code quality** - follows lint rules and best practices

---

## 🏆 **SUCCESS METRICS**

- ✅ **Zero breaking changes** to existing functionality
- ✅ **95%+ UI consistency** across Android manufacturers
- ✅ **Professional-grade responsive design**
- ✅ **WCAG AAA accessibility compliance**
- ✅ **Material Design 3 standards** implementation
- ✅ **Optimal performance** across all devices

---

## 🔧 **IMPLEMENTATION EXAMPLES**

### **Correct DesignSystem Usage**
```dart
// ✅ CORRECT - Using DesignSystem constants
Container(
  padding: EdgeInsets.all(DesignSystem.spaceM),
  margin: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceS,
    vertical: DesignSystem.spaceXS,
  ),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    elevation: DesignSystem.elevationS,
  ),
  child: Text(
    'Content',
    style: TextStyle(
      fontSize: DesignSystem.fontSizeM,
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    ),
  ),
)

// ❌ INCORRECT - Hardcoded values
Container(
  padding: EdgeInsets.all(16.0),
  margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(12.0),
    elevation: 2.0,
  ),
  child: Text(
    'Content',
    style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.w600),
  ),
)
```

### **Proper State Management**
```dart
// ✅ CORRECT - Using Riverpod providers
final selectedTabProvider = StateProvider<int>((ref) => 0);

class TabWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = ref.watch(selectedTabProvider);

    return TabBar(
      onTap: (index) => ref.read(selectedTabProvider.notifier).state = index,
      tabs: tabs,
    );
  }
}

// ❌ INCORRECT - Direct state management without providers
class TabWidget extends StatefulWidget {
  @override
  _TabWidgetState createState() => _TabWidgetState();
}

class _TabWidgetState extends State<TabWidget> {
  int selectedTab = 0;

  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: (index) => setState(() => selectedTab = index),
      tabs: tabs,
    );
  }
}
```

### **Adaptive Component Usage**
```dart
// ✅ CORRECT - Platform-adaptive components
AdaptiveListTile(
  leading: Icon(AdaptiveIcons.settings),
  title: Text('Settings'),
  trailing: Icon(AdaptiveIcons.chevronRight),
  onTap: () => AdaptiveNavigation.push(context, SettingsPage()),
)

// ❌ INCORRECT - Platform-specific hardcoding
ListTile(
  leading: Icon(Platform.isIOS ? CupertinoIcons.settings : Icons.settings),
  title: Text('Settings'),
  trailing: Icon(Platform.isIOS ? CupertinoIcons.chevron_right : Icons.chevron_right),
  onTap: () => Navigator.push(
    context,
    Platform.isIOS
      ? CupertinoPageRoute(builder: (context) => SettingsPage())
      : MaterialPageRoute(builder: (context) => SettingsPage()),
  ),
)
```

---

## 📚 **COMMON PATTERNS & SOLUTIONS**

### **Context Menu Modifications**
```dart
// ✅ CORRECT - Preserving functionality while improving UX
Widget _buildCompactTabBar() {
  return Row(
    children: [
      // Action buttons section
      Expanded(
        flex: 3,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _buildCompactActionButtons(),
        ),
      ),
      SizedBox(width: DesignSystem.spaceXS),
      // Tabs section with icons and tooltips
      Expanded(
        flex: 2,
        child: TabBar(
          tabs: _tabLabels.map((label) => Tab(
            child: Tooltip(
              message: _getTabTooltip(label),
              child: Icon(_getTabIcon(label), size: 16),
            ),
          )).toList(),
        ),
      ),
    ],
  );
}
```

### **Error Handling Pattern**
```dart
// ✅ CORRECT - Comprehensive error handling
Future<void> performOperation() async {
  try {
    setState(() => isLoading = true);

    final result = await riskyOperation();

    if (mounted) {
      setState(() {
        data = result;
        isLoading = false;
      });
    }
  } catch (e) {
    AnxLog.severe('Operation failed: $e');

    if (mounted) {
      setState(() => isLoading = false);
      AnxToast.show(L10n.of(context).operation_failed);
    }
  }
}
```

### **Responsive Design Pattern**
```dart
// ✅ CORRECT - Responsive layout with DesignSystem
Widget _buildResponsiveLayout(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  final isCompact = screenWidth < DesignSystem.breakpointTablet;

  return Container(
    padding: DesignSystem.getAdaptivePadding(context),
    child: isCompact
      ? _buildCompactLayout()
      : _buildExpandedLayout(),
  );
}
```

---

## 🎯 **TROUBLESHOOTING GUIDE**

### **Common Issues & Solutions**

#### **Issue: Hardcoded Values**
```dart
// ❌ Problem
padding: EdgeInsets.all(16.0)

// ✅ Solution
padding: EdgeInsets.all(DesignSystem.spaceM)
```

#### **Issue: Platform Inconsistency**
```dart
// ❌ Problem
Navigator.push(context, MaterialPageRoute(...))

// ✅ Solution
AdaptiveNavigation.push(context, destinationPage)
```

#### **Issue: Poor Performance**
```dart
// ❌ Problem - Rebuilds unnecessarily
Widget build(BuildContext context) {
  return ExpensiveWidget(
    data: computeExpensiveData(), // Called every rebuild
  );
}

// ✅ Solution - Cache expensive computations
Widget build(BuildContext context) {
  return ExpensiveWidget(
    data: _cachedData ??= computeExpensiveData(),
  );
}
```

#### **Issue: Missing Accessibility**
```dart
// ❌ Problem
IconButton(icon: Icon(Icons.settings), onPressed: onTap)

// ✅ Solution
IconButton(
  icon: Icon(Icons.settings),
  onPressed: onTap,
  tooltip: 'Settings',
  semanticLabel: 'Open settings',
)
```

---

## 📖 **QUICK REFERENCE**

### **Essential Imports**
```dart
// Core Flutter
import 'package:flutter/material.dart';

// State Management
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project Core
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
```

### **Key Constants**
```dart
// Spacing
DesignSystem.spaceXS   // 4.0
DesignSystem.spaceS    // 8.0
DesignSystem.spaceM    // 16.0
DesignSystem.spaceL    // 24.0
DesignSystem.spaceXL   // 32.0

// Font Sizes - Standard Scale
DesignSystem.fontSizeXS  // 10.0 - Captions, fine print
DesignSystem.fontSizeS   // 12.0 - Body small, labels
DesignSystem.fontSizeM   // 14.0 - Body medium, default text
DesignSystem.fontSizeL   // 16.0 - Body large, primary text
DesignSystem.fontSizeXL  // 18.0 - Title medium, headings

// Font Sizes - Headings & Display
DesignSystem.fontSizeHeadingM  // 24.0 - Headline medium
DesignSystem.fontSizeDisplayL  // 45.0 - Display large (app titles)

// Font Sizes - Chinese Language
DesignSystem.fontSizeChineseM  // 48.0 - Reading interface
DesignSystem.fontSizePinyinM   // 18.0 - Pinyin pronunciation

// Border Radius
DesignSystem.radiusS   // 4.0
DesignSystem.radiusM   // 8.0
DesignSystem.radiusL   // 12.0

// Elevation
DesignSystem.elevationS  // 2.0
DesignSystem.elevationM  // 4.0
DesignSystem.elevationL  // 8.0
```

### **Performance Logging Modes**
```dart
PerformanceLoggingMode.quiet    // Clean development (default)
PerformanceLoggingMode.minimal  // Basic info only
PerformanceLoggingMode.verbose  // Performance debugging
PerformanceLoggingMode.debug    // Full analysis
```

---

## 📊 **QUALITY METRICS & ACHIEVEMENTS (2025)**

### **Performance Improvements**
- **✅ 1,865 const constructors** - Optimal widget performance
- **✅ 959 DesignSystem references** - Zero hardcoded values
- **✅ 13.5s build time** - Efficient compilation
- **✅ 554 remaining issues** - Down from thousands (major improvement)

### **Code Quality Standards**
- **✅ Enhanced lint rules** - Automatic violation detection
- **✅ Cross-manufacturer consistency** - Samsung, OnePlus, Xiaomi, Huawei
- **✅ WCAG AAA compliance** - 7:1 color contrast ratio
- **✅ Material 3 standards** - Modern design implementation

### **Architecture Excellence**
- **✅ Dual design system** - DesignSystem + Pixel-Perfect adjustments
- **✅ Responsive design** - Adaptive across all screen sizes
- **✅ Type-safe patterns** - Comprehensive error prevention
- **✅ Professional documentation** - Complete development guidelines

---

## 🏆 **FINAL REMINDERS**

### **Before Every Commit**
1. ✅ Run `dart format` to ensure consistent formatting
2. ✅ Run `flutter analyze` to check for lint issues
3. ✅ Test on multiple screen sizes and manufacturers
4. ✅ Verify accessibility with screen reader
5. ✅ Confirm no breaking changes to existing functionality

### **Before Every Pull Request**
1. ✅ Update documentation if needed
2. ✅ Add appropriate tests for new functionality
3. ✅ Verify cross-platform compatibility
4. ✅ Check performance impact
5. ✅ Ensure security best practices are followed

### **Remember**
- **User experience comes first** - always prioritize functionality preservation
- **Space efficiency matters** - especially for mobile interfaces
- **Accessibility is mandatory** - not optional
- **Performance is critical** - monitor and optimize continuously
- **Consistency is key** - follow established patterns

---

*These comprehensive guidelines ensure consistent, professional, and high-quality development for the DassoShu Reader project. Follow them religiously to deliver the best possible results and maintain the project's excellence standards.*
